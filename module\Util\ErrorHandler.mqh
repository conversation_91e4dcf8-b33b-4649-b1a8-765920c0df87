#property strict

//+------------------------------------------------------------------+
//| Error<PERSON><PERSON>ler class definition                                    |
//+------------------------------------------------------------------+
class ErrorHandler
  {
private:
   string   m_last_error;
   string   m_error_log[];
   int      m_error_count;
   int      m_max_errors;
   static ErrorHandler* g_instance;

    // Private constructor
    ErrorHandler()
    {
        m_last_error = "";
        m_error_count = 0;
        m_max_errors = 10;
        ArrayResize(m_error_log, m_max_errors);
    }
    
public:
   // Singleton instance getter
   static ErrorHandler* GetInstance()
     {
      if(g_instance == NULL)
         g_instance = new ErrorHandler();
      return g_instance;
     }

    void SetMaxErrors(int maxErr)
    {
        if(maxErr > 0)
        {
            m_max_errors = maxErr;
            ArrayResize(m_error_log, m_max_errors);
            if(m_error_count > m_max_errors)
            m_error_count = m_max_errors;
        }
    }

   void HandleError(string error)
     {
      m_last_error = error;
      if(m_error_count < m_max_errors)
      {
        m_error_log[m_error_count] = error;
        m_error_count++;
      }
      else
      {
       // Shift all entries left by one to make room for the new error
       for(int i = 1; i < m_error_count; i++)
        m_error_log[i-1] = m_error_log[i];
        m_error_log[m_max_errors - 1] = error; // Add new error at the end
      }
      LogError(error);
     }

   void LogError(string error)
     {
      Print("Error logged: ", error);
     }

   void ClearErrors()
     {
      ArrayFree(m_error_log);
      ArrayResize(m_error_log, m_max_errors);
      m_error_count = 0;
      m_last_error = "";
     }

   string GetLastError()
     {
      return m_last_error;
     }

   int GetErrorCount()
     {
      return m_error_count;
     }
  };

  // Initialize static instance pointer
ErrorHandler* ErrorHandler::g_instance = NULL;