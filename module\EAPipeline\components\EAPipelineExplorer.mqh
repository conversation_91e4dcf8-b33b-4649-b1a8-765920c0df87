//+------------------------------------------------------------------+
//|                                          EAPipelineExplorer.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EAPipelineManager.mqh"
#include "../../PipelineAdvance/PipelineComposite.mqh"
#include "../../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| EAPipelineVisitor 類 - 訪問者模式基類                             |
//+------------------------------------------------------------------+
class EAPipelineVisitor
{
public:
    // 構造函數
    EAPipelineVisitor() {}

    // 析構函數
    virtual ~EAPipelineVisitor() {}

    // 訪問方法，由子類實現
    virtual void Visit(IPipeline* pipeline) = 0;
};

//+------------------------------------------------------------------+
//| 具體訪問者類                                                     |
//+------------------------------------------------------------------+

// 查找特定類型的流水線的訪問者
class FindByTypeVisitor : public EAPipelineVisitor
{
private:
    string m_targetType;
    EAPipelineManager* m_manager;
    EAPipelineItem* m_results[];
    int m_resultCount;

public:
    // 構造函數
    FindByTypeVisitor(const string type, EAPipelineManager* manager)
    : m_targetType(type), m_manager(manager), m_resultCount(0)
    {
        ArrayResize(m_results, 0);
    }

    // 析構函數
    ~FindByTypeVisitor() {}

    // 訪問方法
    void Visit(IPipeline* pipeline)
    {
        if(pipeline != NULL && pipeline.GetType() == m_targetType)
        {
            // 獲取流水線名稱
            string name = pipeline.GetName();
            // 從管理器中獲取對應的 EAPipelineItem
            EAPipelineItem* item = m_manager.GetPipelineByName(name);
            if(item != NULL)
            {
                // 添加到結果數組
                ArrayResize(m_results, m_resultCount + 1);
                m_results[m_resultCount] = item;
                m_resultCount++;
            }
        }
    }

    // 獲取結果
    int GetResults(EAPipelineItem* &results[])
    {
        ArrayResize(results, m_resultCount);
        for(int i = 0; i < m_resultCount; i++)
        {
            results[i] = m_results[i];
        }
        return m_resultCount;
    }
};

// 查找特定名稱的流水線的訪問者
class FindByNameVisitor : public EAPipelineVisitor
{
private:
    string m_targetName;
    EAPipelineManager* m_manager;
    EAPipelineItem* m_result;
    bool m_found;

public:
    // 構造函數
    FindByNameVisitor(const string name, EAPipelineManager* manager)
    : m_targetName(name), m_manager(manager), m_result(NULL), m_found(false) {}

    // 析構函數
    ~FindByNameVisitor() {}

    // 訪問方法
    void Visit(IPipeline* pipeline)
    {
        if(pipeline != NULL && pipeline.GetName() == m_targetName && !m_found)
        {
            // 獲取流水線名稱
            string name = pipeline.GetName();
            // 從管理器中獲取對應的 EAPipelineItem
            EAPipelineItem* item = m_manager.GetPipelineByName(name);
            if(item != NULL)
            {
                // 保存結果
                m_result = item;
                m_found = true;
            }
        }
    }

    // 獲取結果
    EAPipelineItem* GetResult()
    {
        return m_result;
    }
};

// 獲取所有流水線的訪問者
class AllPipelinesVisitor : public EAPipelineVisitor
{
private:
    EAPipelineManager* m_manager;
    EAPipelineItem* m_results[];
    int m_resultCount;

public:
    // 構造函數
    AllPipelinesVisitor(EAPipelineManager* manager)
    : m_manager(manager), m_resultCount(0)
    {
        ArrayResize(m_results, 0);
    }

    // 析構函數
    ~AllPipelinesVisitor() {}

    // 訪問方法
    void Visit(IPipeline* pipeline)
    {
        if(pipeline != NULL)
        {
            // 獲取流水線名稱
            string name = pipeline.GetName();
            // 從管理器中獲取對應的 EAPipelineItem
            EAPipelineItem* item = m_manager.GetPipelineByName(name);
            if(item != NULL)
            {
                // 添加到結果數組
                ArrayResize(m_results, m_resultCount + 1);
                m_results[m_resultCount] = item;
                m_resultCount++;
            }
        }
    }

    // 獲取結果
    int GetResults(EAPipelineItem* &results[])
    {
        ArrayResize(results, m_resultCount);
        for(int i = 0; i < m_resultCount; i++)
        {
            results[i] = m_results[i];
        }
        return m_resultCount;
    }
};

// 計數訪問者
class CountVisitor : public EAPipelineVisitor
{
private:
    int m_count;

public:
    // 構造函數
    CountVisitor() : m_count(0) {}

    // 析構函數
    ~CountVisitor() {}

    // 訪問方法
    void Visit(IPipeline* pipeline)
    {
        if(pipeline != NULL)
        {
            m_count++;
        }
    }

    // 獲取結果
    int GetCount()
    {
        return m_count;
    }
};

// 獲取層次結構訊息的訪問者
class HierarchyMessageVisitor : public EAPipelineVisitor
{
private:
    int m_level;
    string m_output;

public:
    // 構造函數
    HierarchyMessageVisitor() : m_level(0), m_output("流水線層次結構:\n") {}

    // 析構函數
    ~HierarchyMessageVisitor() {}

    // 訪問方法
    void Visit(IPipeline* pipeline)
    {
        if(pipeline != NULL)
        {
            // 添加縮進
            string indent = "";
            for(int i = 0; i < m_level; i++)
            {
                indent += "  ";
            }

            // 添加流水線信息
            m_output += indent + "- " + pipeline.GetName() + " (" + pipeline.GetType() + ")\n";

            // 如果是複合流水線，增加層級
            if(pipeline.GetType() == "PipelineComposite" || pipeline.GetType() == "EACompoundPipeline")
            {
                m_level++;
            }
        }
    }

    // 獲取結果
    string GetOutput()
    {
        return m_output;
    }
};

//+------------------------------------------------------------------+
//| EAPipelineExplorer 類 - 深度優先遍歷 EAPipelineManager              |
//+------------------------------------------------------------------+
class EAPipelineExplorer
{
private:
    EAPipelineManager* m_manager;

    // 檢查流水線是否為複合流水線
    bool IsComposite(IPipeline* pipeline)
    {
        if(pipeline == NULL)
            return false;

        return pipeline.GetType() == "PipelineComposite" || pipeline.GetType() == "EACompoundPipeline";
    }

    // 獲取複合流水線的子流水線
    int GetChildren(PipelineComposite* composite, IPipeline* &children[])
    {
        if(composite == NULL)
            return 0;

        return composite.GetAllItems(children);
    }

    // 深度優先遍歷輔助方法
    void DFSTraversal(IPipeline* root, EAPipelineVisitor* visitor = NULL)
    {
        if(root == NULL)
            return;

        // 調用訪問者的訪問方法
        if(visitor != NULL)
            visitor.Visit(root);

        // 如果是複合流水線，遞歸遍歷子流水線
        if(IsComposite(root))
        {
            PipelineComposite* composite = dynamic_cast<PipelineComposite*>(root);
            if(composite != NULL)
            {
                IPipeline* children[];
                int count = GetChildren(composite, children);

                for(int i = 0; i < count; i++)
                {
                    if(children[i] != NULL)
                    {
                        DFSTraversal(children[i], visitor);
                    }
                }
            }
        }
    }

    // 遍歷 EAPipelineManager 中的所有流水線
    void Traverse(EAPipelineVisitor* visitor = NULL)
    {
        if(m_manager == NULL)
            return;

        // 獲取所有流水線名稱
        string pipelineNames[];
        int count = m_manager.GetAllPipelineNames(pipelineNames);

        // 遍歷所有流水線
        for(int i = 0; i < count; i++)
        {
            EAPipelineItem* item = m_manager.GetPipelineByName(pipelineNames[i]);
            if(item != NULL && item.GetValue() != NULL)
            {
                IPipeline* pipeline = item.GetValue();
                // 對每個流水線進行深度優先遍歷
                DFSTraversal(pipeline, visitor);
            }

            // 釋放資源
            if(item != NULL)
            {
                delete item;
            }
        }
    }

public:
    EAPipelineExplorer(EAPipelineManager* manager = NULL)
    : m_manager(manager?manager:EAPipelineManager::GetInstance()) {}

    ~EAPipelineExplorer() {}

    // 查找特定名稱的流水線（包括嵌套的）
    EAPipelineItem* FindByName(const string name)
    {
        if(name == "" || m_manager == NULL)
            return new EAPipelineItem(new RegistryItem<IPipeline*>("", "NULL", "NULL", NULL, "NULL"));

        // 創建訪問者
        FindByNameVisitor visitor(name, m_manager);

        // 遍歷所有流水線
        Traverse(&visitor);

        // 返回結果
        return visitor.GetResult();
    }

    // 查找特定類型的流水線（包括嵌套的）
    int FindByType(const string type, EAPipelineItem* &results[])
    {
        if(type == "" || m_manager == NULL)
            return 0;

        // 創建訪問者
        FindByTypeVisitor visitor(type, m_manager);

        // 遍歷所有流水線
        Traverse(&visitor);

        // 獲取結果
        return visitor.GetResults(results);
    }

    // 獲取所有流水線（包括嵌套的）
    int AllPipelines(EAPipelineItem* &pipelines[])
    {
        if(m_manager == NULL)
            return 0;

        // 創建訪問者
        AllPipelinesVisitor visitor(m_manager);

        // 遍歷所有流水線
        Traverse(&visitor);

        // 獲取結果
        return visitor.GetResults(pipelines);
    }

    // 計算流水線總數（包括嵌套的）
    int CountAll()
    {
        if(m_manager == NULL)
            return 0;

        // 創建訪問者
        CountVisitor visitor;

        // 遍歷所有流水線
        Traverse(&visitor);

        // 返回結果
        return visitor.GetCount();
    }

    // 獲取層次結構訊息
    string HierarchyMessage(bool print = false)
    {
        if(m_manager == NULL)
            return "";

        // 創建訪問者
        HierarchyMessageVisitor visitor;

        // 遍歷所有流水線
        Traverse(&visitor);

        // 打印結果
        if(print)
            Print(visitor.GetOutput());

        return visitor.GetOutput();
    }
};
