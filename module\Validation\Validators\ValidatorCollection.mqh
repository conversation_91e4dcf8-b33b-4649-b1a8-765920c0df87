#property strict

#include <../../../src/mql4-lib-master/Collection/Vector.mqh>  // 引入 Vector 類

//+------------------------------------------------------------------+
//| ValidatorCollection                                                |
//| 功能：集合所有驗證器並提供統一的驗證器管理介面                          |
//| 版本：1.0                                                          |
//+------------------------------------------------------------------+

// 包含所有驗證器
#include "RangeValidator.mqh"
#include "StringValidator.mqh"
#include "RequiredValidator.mqh"

//+------------------------------------------------------------------+
//| 驗證器工廠類                                                        |
//| 提供創建各種驗證器的統一介面                                         |
//+------------------------------------------------------------------+
class CValidatorFactory
{
public:
    //--- 範圍驗證器
    template<typename T>
    static CRangeValidator<T>* CreateRangeValidator(T value, T min, T max, const string field)
    {
        return new CRangeValidator<T>(value, min, max, field);
    }

    //--- 字串長度驗證器
    static CStringValidator* CreateStringValidator(string value, 
        int minLength, int maxLength, const string field)
    {
        return new CStringValidator(value, minLength, maxLength, field);
    }

    //--- 必填欄位驗證器
    static CRequiredValidator* CreateRequiredValidator(string value, const string field)
    {
        return new CRequiredValidator(value, field);
    }
};

//+------------------------------------------------------------------+
//| 驗證器組合類                                                        |
//| 允許組合多個驗證器並統一執行驗證                                     |
//+------------------------------------------------------------------+
class CValidatorComposite : public CBaseValidator
{
private:
    Vector<IValidator*>* m_validators;  // 使用 Vector 存儲驗證器

public:
    // 建構函數
    CValidatorComposite() 
        : CBaseValidator("ValidatorComposite", VALIDATOR_TYPE_CUSTOM)
    {
        m_validators = new Vector<IValidator*>(true);  // true 表示 Vector 擁有其元素的所有權
    }
    
    // 解構函數
    ~CValidatorComposite()
    {
        if(m_validators != NULL)
        {
            delete m_validators;
            m_validators = NULL;
        }
    }

    // 添加驗證器
    void Add(IValidator* validator)
    {
        if(validator != NULL)
        {
            m_validators.add(validator);
        }
    }
    
    // 移除驗證器
    bool Remove(IValidator* validator)
    {
        if(validator == NULL)
        {
            return false;
        }
        
        for(int i = 0; i < m_validators.size(); i++)
        {
            if(m_validators[i] == validator)
            {
                m_validators.remove(i);
                return true;
            }
        }
        
        return false;
    }
    
    // 清空所有驗證器
    void Clear()
    {
        m_validators.clear();
    }
    
    // 獲取驗證器數量
    int GetCount() const
    {
        return m_validators.size();
    }
    
    // 獲取指定索引的驗證器
    IValidator* GetValidator(const int index)
    {
        if(index < 0 || index >= m_validators.size())
        {
            return NULL;
        }
        
        return m_validators[index];
    }

    // 執行所有驗證
    virtual CValidationResult* Validate() override
    {
        CValidationResult* result = new CValidationResult();
        
        for(int i = 0; i < m_validators.size(); i++)
        {
            if(m_validators[i].IsEnabled())
            {
                CValidationResult* tempResult = m_validators[i].Validate();
                if(!tempResult.IsValid())
                {
                    result.Merge(tempResult);
                    delete tempResult;
                    return result;  // 如果任何驗證失敗，立即返回
                }
                delete tempResult;
            }
        }
        
        return result;
    }
};
