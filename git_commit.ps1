$logFile = "C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\EA_Wizard\git_commit.log"

# Clear log file
"" | Out-File -FilePath $logFile

# Set working directory
Set-Location -Path "C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\EA_Wizard"
"Set working directory to EA_Wizard" | Out-File -FilePath $logFile -Append

# Check current branch
$currentBranch = git branch
"Current branches:" | Out-File -FilePath $logFile -Append
$currentBranch | Out-File -FilePath $logFile -Append

# Check git status
"Git status:" | Out-File -FilePath $logFile -Append
$gitStatus = git status
$gitStatus | Out-File -FilePath $logFile -Append

# Add all changes
"Adding all changes..." | Out-File -FilePath $logFile -Append
$gitAdd = git add .
$gitAdd | Out-File -FilePath $logFile -Append

# Commit changes
"Committing changes..." | Out-File -FilePath $logFile -Append
$gitCommit = git commit -m "Initial commit to feature/EAPipeline branch"
$gitCommit | Out-File -FilePath $logFile -Append

# Verify status after commit
"Git status after commit:" | Out-File -FilePath $logFile -Append
$gitStatusAfter = git status
$gitStatusAfter | Out-File -FilePath $logFile -Append

"Git operations completed." | Out-File -FilePath $logFile -Append
