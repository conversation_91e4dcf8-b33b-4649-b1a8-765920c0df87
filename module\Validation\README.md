# 驗證模組 (Validation Module)

這是一個用於 EA_Wizard 專案的驗證模組，提供了一套完整的驗證工具，用於驗證各種輸入和參數。

## 功能特點

- 支持多種驗證類型（必填、範圍、字符串等）
- 使用組合模式實現複雜的驗證邏輯
- 提供驗證群組管理多個相關的驗證器
- 支持流水線架構，可以作為流水線階段使用
- 提供詳細的驗證結果和錯誤訊息

## 文件結構

- `ValidationConfig.mqh` - 驗證配置常量
- `ValidationResult.mqh` - 驗證結果類
- `IValidator.mqh` - 驗證器接口和基類
- `Validators/RequiredValidator.mqh` - 必填驗證器
- `Validators/RangeValidator.mqh` - 範圍驗證器
- `Validators/StringValidator.mqh` - 字符串驗證器
- `Validators/ValidatorCollection.mqh` - 驗證器集合
- `ValidationGroup.mqh` - 驗證群組
- `ValidationPipeline.mqh` - 驗證流水線階段
- `ValidationExample.mqh` - 使用示例
- `ValidationTest.mq4` - 測試腳本

## 使用方法

### 基本驗證

```cpp
// 創建驗證器
CRequiredValidator* requiredValidator = new CRequiredValidator("EURUSD", "Symbol");

// 執行驗證
CValidationResult* result = requiredValidator.Validate();

// 檢查結果
if(result.IsValid()) {
    Print("驗證通過");
} else {
    Print("驗證失敗: ", result.GetMessage());
}

// 清理資源
delete result;
delete requiredValidator;
```

### 使用驗證器工廠

```cpp
// 使用工廠創建驗證器
IValidator* validator = CValidatorFactory::CreateRangeValidator<double>(0.5, 0.1, 1.0, "Lot");

// 執行驗證
CValidationResult* result = validator.Validate();

// 檢查結果
if(!result.IsValid()) {
    Print("驗證失敗: ", result.GetMessage());
}

// 清理資源
delete result;
delete validator;
```

### 使用驗證器組合

```cpp
// 創建驗證器組合
CValidatorComposite* composite = new CValidatorComposite();

// 添加驗證器
composite.Add(CValidatorFactory::CreateRequiredValidator("EURUSD", "Symbol"));
composite.Add(CValidatorFactory::CreateRangeValidator<double>(0.5, 0.1, 1.0, "Lot"));

// 執行驗證
CValidationResult* result = composite.Validate();

// 檢查結果
if(!result.IsValid()) {
    Print("驗證失敗: ", result.GetMessage());
}

// 清理資源
delete result;
delete composite;
```

### 使用驗證群組

```cpp
// 創建驗證群組
CValidationGroup* group = new CValidationGroup("TradeValidation");

// 添加驗證器
group.AddValidator(CValidatorFactory::CreateRequiredValidator("EURUSD", "Symbol"));
group.AddValidator(CValidatorFactory::CreateRangeValidator<double>(0.5, 0.1, 1.0, "Lot"));

// 設置是否在第一個錯誤時停止
group.SetStopOnFirstError(true);

// 執行驗證
CValidationResult* result = group.ValidateAll();

// 檢查結果
if(!result.IsValid()) {
    Print("驗證失敗: ", result.GetMessage());
}

// 清理資源
delete result;
delete group;
```

### 使用驗證流水線階段

```cpp
// 創建流水線管理器
PipelineManager<bool, void*>* manager = new PipelineManager<bool, void*>();

// 創建驗證流水線階段
CValidationPipeline* validationPipeline = new CValidationPipeline("TradeValidation");

// 添加驗證器
validationPipeline.AddValidator(CValidatorFactory::CreateRequiredValidator("EURUSD", "Symbol"));
validationPipeline.AddValidator(CValidatorFactory::CreateRangeValidator<double>(0.5, 0.1, 1.0, "Lot"));

// 設置驗證失敗時是否繼續流水線
validationPipeline.SetContinueOnError(false);

// 添加階段到流水線
manager.AddPipeline(validationPipeline);

// 執行流水線
bool result = manager.Execute();

// 檢查結果
if(!result) {
    Print("流水線執行失敗");
}

// 清理資源
delete manager; // 這將自動刪除所有流水線階段
```

## 擴展

要添加新的驗證器類型，只需要：

1. 創建一個新的驗證器類，繼承自 `CBaseValidator`
2. 實現 `Validate()` 方法
3. 在 `ValidatorCollection.mqh` 中添加相應的工廠方法

## 注意事項

- 所有驗證器都需要手動釋放記憶體
- 驗證結果對象也需要手動釋放記憶體
- 使用驗證群組或驗證流水線階段時，不需要手動釋放添加的驗證器，它們會在群組或階段被釋放時自動釋放
